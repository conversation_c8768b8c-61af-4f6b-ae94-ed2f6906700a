//
//  AccountSettingViewController.swift
//  <PERSON><PERSON><PERSON><PERSON>
//
//  Created by yongsheng ye on 2025/3/20.
//

//账号设置
import UIKit
import SnapKit

// 账号设置项模型
struct AccountSettingItem {
    let icon: String // 图标名称
    let title: String // 标题
    let rightText: String? // 右侧文本（可选）
    let showArrow: Bool // 是否显示箭头
    let isHighlighted: Bool // 是否高亮显示
    let action: (() -> Void)? // 点击操作
}

// 账号设置分组模型
struct AccountSettingSection {
    var items: [AccountSettingItem]
    let showFooter: Bool // 是否在分组底部显示间隔
}

class AccountSettingViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    // 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.register(AccountSettingCell.self, forCellReuseIdentifier: "AccountSettingCell")
        tableView.contentInsetAdjustmentBehavior = .never
        // 设置表格视图的内边距
        tableView.contentInset = UIEdgeInsets(top: 12, left: 0, bottom: 12, right: 0)
        return tableView
    }()
    
    // 设置数据
    private var sections: [AccountSettingSection] = []
    // 新增：用于存储从 API 获取的数据
    private var accountInfo: AccountSetInfoData?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置标题
        navTitle = "账号设置"
        
        // 设置视图背景色
        view.backgroundColor = .white // 保持白色背景，tableView 背景为 F5F5F5
        
        // 设置表格视图
        setupTableView()
        
        // 设置初始数据结构 (使用占位符)
        setupInitialDataStructure()
        
        // 获取账户设置信息
        fetchAccountSettings()
    }
    
    private func setupTableView() {
        // 添加表格视图到内容视图
        contentView.addSubview(tableView)
        
        // 设置表格视图约束
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // 修改：设置初始数据结构，使用占位符或 nil
    private func setupInitialDataStructure() {
        // 第一组：社交账号绑定
        let section1 = AccountSettingSection(items: [
            AccountSettingItem(icon: "account_wechat", title: "微信绑定", rightText: "加载中...", showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToWechatBinding()
            }),
            AccountSettingItem(icon: "account_phone", title: "手机号绑定", rightText: "加载中...", showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToPhoneBinding()
            })
        ], showFooter: true)
        
        // 第二组：安全设置
        let section2 = AccountSettingSection(items: [
            AccountSettingItem(icon: "account_password", title: "密码设置", rightText: "加载中...", showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToPasswordSetting()
            }),
            AccountSettingItem(icon: "account_devices", title: "登录设备管理", rightText: "加载中...", showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToDeviceManagement()
            }),
            AccountSettingItem(icon: "account_privacy", title: "隐私设置", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToPrivacySetting()
            })
        ], showFooter: true)
        
        // 第三组：注销账号
        let section3 = AccountSettingSection(items: [
            AccountSettingItem(icon: "account_deactivate", title: "注销账户", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToDeleteAccount()
            })
        ], showFooter: false)
        
        // 设置数据
        sections = [section1, section2, section3]
        
        // 刷新表格视图以显示初始结构
        tableView.reloadData()
    }
    
    // 新增：获取并更新账户设置信息
    private func fetchAccountSettings() {
//        showLoading() // 显示加载指示器
        APIManager.shared.getAccountSetInfo { [weak self] result in
            guard let self = self else { return }
//            self.hideLoading() // 隐藏加载指示器
            
            switch result {
            case .success(let response):
                if response.isSuccess, let data = response.data {
                    self.accountInfo = data
                    self.updateSectionsWithData(data)
                    self.tableView.reloadData()
                } else {
                    // 显示错误信息
                    self.showToast( response.displayMessage)
                    self.updateSectionsWithError() // 更新UI显示错误状态
                    self.tableView.reloadData()
                }
            case .failure(let error):
                // 处理网络或其他错误
                self.showToast(error.localizedDescription)
                self.updateSectionsWithError() // 更新UI显示错误状态
                self.tableView.reloadData()
            }
        }
    }
    
    // 新增：根据 API 数据更新 sections
    private func updateSectionsWithData(_ data: AccountSetInfoData) {
        guard sections.count == 3,
              sections[0].items.count == 2,
              sections[1].items.count == 3 else {
            print("Error: Section structure mismatch.")
            return
        }
        
        // 更新微信绑定状态 (假设 data.vxBang 是 Bool)
        sections[0].items[0] = sections[0].items[0].copy(rightText: data.vxBang ? "已绑定" : "未绑定")
        
        // 更新手机号绑定状态 (使用 data.phone)
        sections[0].items[1] = sections[0].items[1].copy(rightText: data.phone.isEmpty ? "未绑定" : data.phone) // 直接显示脱敏手机号
        
        // 更新密码设置状态 (假设 data.psd 是 Bool)
        sections[1].items[0] = sections[1].items[0].copy(rightText: data.psd ? "已设置" : "未设置")
        
        // 更新登录设备管理状态 (使用 data.loginDeviceCount)
        sections[1].items[1] = sections[1].items[1].copy(rightText: "\(data.loginDeviceCount)台设备在线")
        
        // 隐私设置通常没有右侧文本，保持不变
        // sections[1].items[2] = sections[1].items[2].copy(...)
    }
    
    // 新增：在加载失败时更新 sections 的显示
    private func updateSectionsWithError() {
         guard sections.count == 3,
               sections[0].items.count == 2,
               sections[1].items.count == 3 else {
             print("Error: Section structure mismatch.")
             return
         }
         let errorText = "加载失败"
         sections[0].items[0] = sections[0].items[0].copy(rightText: errorText)
         sections[0].items[1] = sections[0].items[1].copy(rightText: errorText)
         sections[1].items[0] = sections[1].items[0].copy(rightText: errorText)
         sections[1].items[1] = sections[1].items[1].copy(rightText: errorText)
    }
    
    // MARK: - UITableViewDataSource
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "AccountSettingCell", for: indexPath) as! AccountSettingCell
        let item = sections[indexPath.section].items[indexPath.row]
        cell.configure(with: item)
        return cell
    }
    
    // MARK: - UITableViewDelegate
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = sections[indexPath.section].items[indexPath.row]
        item.action?()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 56 // 设置单元格高度为56
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return section == 0 ? 0 : 12 // 第一个分组没有头部，其他分组头部高度为12
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return sections[section].showFooter ? 1 : 0.01 // 如果显示尾部，高度为1，否则为0.01（不能为0）
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        if section == 0 {
            return nil // 第一个分组没有头部
        }
        
        let headerView = UIView()
        headerView.backgroundColor = UIColor(hex: "#F5F5F5") // 设置背景色为浅灰色
        return headerView
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        if !sections[section].showFooter {
            return nil // 如果不显示尾部，返回nil
        }
        
        let footerView = UIView()
        footerView.backgroundColor = UIColor(hex: "#F5F5F5") // 设置背景色为浅灰色
        return footerView
    }
    
    // MARK: - Navigation Methods
    
    private func navigateToWechatBinding() {
        print("导航到微信绑定页面")
        // 实现导航到微信绑定页面的逻辑
    }
    
    private func navigateToPhoneBinding() {
        print("导航到手机号绑定页面")
        // 实现导航到手机号绑定页面的逻辑
        let vmpnVC = VerifyMobilePhoneNumberViewController()
        vmpnVC.verificationType = .verifyOldPhoneForBind
        self.navigationController?.pushViewController(vmpnVC, animated: true)
    }
    
    private func navigateToPasswordSetting() {
        print("导航到密码设置页面")
        // 实现导航到密码设置页面的逻辑
        let vmpnVC = VerifyMobilePhoneNumberViewController()
        vmpnVC.verificationType = .changePassword
        self.navigationController?.pushViewController(vmpnVC, animated: true)
    }
    
    private func navigateToDeviceManagement() {
        print("导航到登录设备管理页面")
        // 实现导航到登录设备管理页面的逻辑
        self.navigationController?.pushViewController(DeviceManagementViewController(), animated: true)
    }
    
    private func navigateToPrivacySetting() {
        print("导航到隐私设置页面")
        // 实现导航到隐私设置页面的逻辑
        self.navigationController?.pushViewController(AccountPrivacyViewController(), animated: true)
    }
    
    private func navigateToDeleteAccount() {
        print("导航到注销账号页面")
        // 直接跳转到注销账号页面
        let deleteAccountVC = DeleteAccountViewController()
        self.navigationController?.pushViewController(deleteAccountVC, animated: true)
    }
}

// MARK: - AccountSettingCell

class AccountSettingCell: UITableViewCell {
    
    // 容器视图
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 图标视图
    private let iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 右侧文本标签
    private let rightTextLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.textAlignment = .right
        return label
    }()
    
    // 箭头图标
    private let arrowImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "setting_arrow")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 分隔线
    private let separatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E7E7E7")
        return view
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 设置背景色
        backgroundColor = UIColor.clear
        selectionStyle = .none
        
        // 添加容器视图
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        // 添加图标视图
        containerView.addSubview(iconImageView)
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 添加标题标签
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.centerY.equalToSuperview()
        }
        
        // 添加箭头图标
        containerView.addSubview(arrowImageView)
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(16)
            make.height.equalTo(16)
        }
        
        // 添加右侧文本标签
        containerView.addSubview(rightTextLabel)
        rightTextLabel.snp.makeConstraints { make in
            make.right.equalTo(arrowImageView.snp.left).offset(-8)
            make.centerY.equalToSuperview()
        }
        
        // 添加分隔线
        containerView.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(52) // 与标题左对齐
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }
    
    func configure(with item: AccountSettingItem) {
        // 设置图标
        if item.icon.contains(".") {
            // 系统图标
            iconImageView.image = UIImage(systemName: item.icon)
            iconImageView.tintColor = item.isHighlighted ? UIColor(hex: "#FF3B30") : UIColor(hex: "#333333")
        } else {
            // 本地图标
            iconImageView.image = UIImage(named: item.icon)
        }
        
        // 设置标题
        titleLabel.text = item.title
        
        // 设置标题颜色
        if item.isHighlighted {
            titleLabel.textColor = UIColor(hex: "#FF3B30") // 红色
        } else {
            titleLabel.textColor = UIColor(hex: "#333333") // 默认颜色
        }
        
        // 设置右侧文本
        rightTextLabel.text = item.rightText
        rightTextLabel.isHidden = item.rightText == nil
        
        // 设置右侧文本颜色
        if item.rightText == "已认证" || item.rightText == "已绑定" {
            rightTextLabel.textColor = UIColor(hex: "#65C27B") // 绿色
        } else if item.rightText == "未绑定" || item.rightText == "未设置" {
            rightTextLabel.textColor = UIColor(hex: "#777777") // 灰色
        } else {
            rightTextLabel.textColor = UIColor(hex: "#777777") // 灰色
        }
        
        // 设置箭头
        arrowImageView.isHidden = !item.showArrow
    }
}

// MARK: - Helper extension for AccountSettingItem to create a modified copy
extension AccountSettingItem {
    func copy(
        icon: String? = nil,
        title: String? = nil,
        rightText: String?? = nil, // Use double optional to differentiate between setting nil and no change
        showArrow: Bool? = nil,
        isHighlighted: Bool? = nil,
        action: (() -> Void)? = nil
    ) -> AccountSettingItem {
        return AccountSettingItem(
            icon: icon ?? self.icon,
            title: title ?? self.title,
            rightText: rightText ?? self.rightText,
            showArrow: showArrow ?? self.showArrow,
            isHighlighted: isHighlighted ?? self.isHighlighted,
            action: action ?? self.action
        )
    }
}


